# 数据库集成说明

## 概述

系统已成功从固定的二维码列表迁移到数据库驱动的验证系统。现在所有的代码验证都通过查询 MySQL 数据库来完成。

## 数据库配置

### 连接信息
- **主机**: sh-cynosdbmysql-grp-qvemfela.sql.tencentcdb.com
- **端口**: 27144
- **用户名**: root
- **数据库**: mptime
- **字符集**: utf8mb4

### 表结构

```sql
CREATE TABLE activation_codes (
    code           varchar(191)                        NOT NULL PRIMARY KEY,
    remaining_uses int                                 NOT NULL,
    open_id        varchar(191)                        NULL,
    prefix         varchar(120)                        NULL,
    created_at     timestamp DEFAULT CURRENT_TIMESTAMP NULL
) COLLATE = utf8mb4_unicode_ci;
```

## 查询逻辑

系统现在使用以下 SQL 查询来验证代码：

```sql
SELECT code, prefix, remaining_uses, created_at
FROM activation_codes
WHERE code = ? AND prefix = 'mobile'
```

### 天数计算逻辑

`remaining_uses` 字段实际表示**总有效天数**，系统会根据创建时间计算实际剩余天数：

1. **总天数**: `remaining_uses` 字段的值
2. **已过天数**: 从 `created_at` 到当前时间的天数
3. **剩余天数**: `总天数 - 已过天数`

### 验证条件
1. 代码必须存在于数据库中
2. 前缀必须为 'mobile'
3. 剩余天数必须大于 0（即 `总天数 - 已过天数 > 0`)

## 代码更改

### 主要文件修改

1. **lib/code.ts**
   - 添加了 MySQL 连接配置
   - 实现了 `validateCodeFromDB()` 函数（基于天数计算）
   - 实现了 `getCodeInfo()` 函数（返回详细的天数信息）
   - 实现了 `isValidCode()` 函数用于向后兼容
   - 添加了 `getDaysFromCreation()` 辅助函数
   - 保留了原有的静态代码列表作为备用

2. **app/api/verify-code/route.ts**
   - 替换静态列表验证为数据库查询
   - 使用 `getCodeInfo()` 获取详细信息
   - 返回剩余天数、总天数、已过天数等信息
   - 为过期代码提供更友好的错误消息
   - 添加了错误处理

3. **app/[code]/preview/page.tsx**
   - 更新为使用数据库验证

4. **app/[code]/[accessCode]/preview/page.tsx**
   - 更新为使用数据库验证

## 功能特性

### 连接池
- 使用 MySQL 连接池提高性能
- 最大连接数: 10
- 自动重连机制

### 错误处理
- 数据库连接失败时的优雅降级
- 详细的错误日志记录
- 用户友好的错误消息

### 向后兼容
- 保留了原有的静态代码列表
- API 接口保持不变
- 前端界面无需修改

## 测试

系统已通过以下测试：

1. **数据库连接测试**: ✅ 成功连接并查询数据
2. **天数计算测试**: ✅ 正确计算剩余天数
3. **API 验证测试**: ✅ 有效、无效和过期代码都能正确响应
4. **页面访问测试**: ✅ 预览页面能正确验证代码

### 测试用例

#### 有效代码
- `75255427`: 创建于8月1日，总30天，剩余27天 ✅
- `78862316`: 创建于8月4日，总30天，剩余30天 ✅

#### 过期代码
- `EXPIRED123`: 创建于35天前，总30天，已过期5天 ❌

#### 无效代码
- 任何不在数据库中的代码

### API 响应示例

**有效代码响应**:
```json
{
  "success": true,
  "count": 1,
  "remainingDays": 27,
  "totalDays": 30,
  "daysPassed": 3
}
```

**过期代码响应**:
```json
{
  "success": false,
  "message": "授权码已过期（已过期 5 天）",
  "count": 1,
  "remainingDays": -5,
  "totalDays": 30,
  "daysPassed": 35
}
```

## 部署注意事项

1. 确保生产环境能访问数据库服务器
2. 检查防火墙设置允许端口 27144
3. 验证数据库凭据在生产环境中的安全性
4. 考虑使用环境变量存储敏感信息

## 监控建议

1. 监控数据库连接状态
2. 记录验证失败的代码以便分析
3. 监控 API 响应时间
4. 设置数据库查询超时告警

## 批量导入结果

已成功将所有静态代码导入数据库：

- **成功导入**: 93 个新代码
- **跳过重复**: 35 个已存在的代码
- **导入失败**: 0 个代码
- **总计处理**: 128 个代码

### 当前数据库状态
- **总代码数**: 96 个
- **有效代码**: 95 个 (30天有效期)
- **过期代码**: 1 个 (测试用过期代码)
- **平均剩余天数**: 29.6 天

## 代码管理工具

提供了 `scripts/manage-codes.js` 脚本用于管理数据库中的代码：

```bash
# 查看所有代码状态
node scripts/manage-codes.js list

# 添加新代码 (默认30天)
node scripts/manage-codes.js add NEW123 60

# 延长代码有效期
node scripts/manage-codes.js extend OLD123 90

# 重置代码创建时间
node scripts/manage-codes.js reset CODE123

# 删除代码
node scripts/manage-codes.js remove CODE123

# 清理过期代码
node scripts/manage-codes.js cleanup 30

# 查看统计信息
node scripts/manage-codes.js stats
```

## 未来改进

1. 添加代码使用次数递减功能
2. 实现代码使用历史记录
3. 添加代码批量生成功能
4. 实现 Web 界面的代码管理
