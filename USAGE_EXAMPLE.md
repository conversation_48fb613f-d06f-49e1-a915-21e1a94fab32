# 使用示例

## API 调用示例

### JavaScript/TypeScript 前端调用

```javascript
async function verifyCode(code) {
  try {
    const response = await fetch('/api/verify-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ code }),
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log(`✅ 代码有效！剩余 ${result.remainingDays} 天`);
      console.log(`总天数: ${result.totalDays}, 已过天数: ${result.daysPassed}`);
      
      // 显示剩余天数给用户
      showSuccessMessage(`授权码有效，剩余 ${result.remainingDays} 天`);
      
      return true;
    } else {
      console.log(`❌ ${result.message}`);
      
      if (result.remainingDays < 0) {
        // 过期代码
        showErrorMessage(`代码已过期 ${Math.abs(result.remainingDays)} 天`);
      } else {
        // 无效代码
        showErrorMessage(result.message);
      }
      
      return false;
    }
  } catch (error) {
    console.error('验证代码时发生错误:', error);
    showErrorMessage('网络错误，请稍后重试');
    return false;
  }
}

// 使用示例
verifyCode('75255427'); // 有效代码
verifyCode('EXPIRED123'); // 过期代码
verifyCode('invalid123'); // 无效代码
```

### React 组件示例

```jsx
import { useState } from 'react';

function CodeVerifier() {
  const [code, setCode] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleVerify = async () => {
    if (!code.trim()) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/verify-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: code.trim() }),
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        message: '网络错误，请稍后重试'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="code-verifier">
      <input
        type="text"
        value={code}
        onChange={(e) => setCode(e.target.value)}
        placeholder="请输入授权码"
        className="border p-2 rounded"
      />
      <button
        onClick={handleVerify}
        disabled={loading || !code.trim()}
        className="ml-2 px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
      >
        {loading ? '验证中...' : '验证'}
      </button>
      
      {result && (
        <div className={`mt-4 p-3 rounded ${
          result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          <div className="font-semibold">
            {result.success ? '✅ 验证成功' : '❌ 验证失败'}
          </div>
          <div>{result.message || (result.success ? `剩余 ${result.remainingDays} 天` : '')}</div>
          
          {result.totalDays > 0 && (
            <div className="text-sm mt-2">
              总天数: {result.totalDays} | 已过天数: {result.daysPassed} | 剩余天数: {result.remainingDays}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default CodeVerifier;
```

## 数据库管理示例

### 添加新的授权码

```sql
-- 添加一个30天有效期的新授权码
INSERT INTO activation_codes (code, remaining_uses, prefix, created_at) 
VALUES ('NEW_CODE_123', 30, 'mobile', NOW());
```

### 查询所有有效代码

```sql
-- 查询所有未过期的 mobile 代码
SELECT 
    code,
    remaining_uses as total_days,
    DATEDIFF(NOW(), created_at) as days_passed,
    (remaining_uses - DATEDIFF(NOW(), created_at)) as remaining_days,
    created_at,
    CASE 
        WHEN (remaining_uses - DATEDIFF(NOW(), created_at)) > 0 THEN '有效'
        ELSE '已过期'
    END as status
FROM activation_codes 
WHERE prefix = 'mobile'
ORDER BY remaining_days DESC;
```

### 清理过期代码

```sql
-- 删除过期超过30天的代码
DELETE FROM activation_codes 
WHERE prefix = 'mobile' 
AND (remaining_uses - DATEDIFF(NOW(), created_at)) < -30;
```

## 常见问题

### Q: 如何修改代码的有效期？
A: 直接更新数据库中的 `remaining_uses` 字段：
```sql
UPDATE activation_codes 
SET remaining_uses = 60 
WHERE code = 'YOUR_CODE_HERE';
```

### Q: 如何重置代码的创建时间？
A: 更新 `created_at` 字段：
```sql
UPDATE activation_codes 
SET created_at = NOW() 
WHERE code = 'YOUR_CODE_HERE';
```

### Q: 如何批量生成代码？
A: 可以使用脚本批量插入：
```sql
INSERT INTO activation_codes (code, remaining_uses, prefix, created_at) VALUES
('CODE001', 30, 'mobile', NOW()),
('CODE002', 30, 'mobile', NOW()),
('CODE003', 30, 'mobile', NOW());
```
