#!/usr/bin/env node

// 代码管理脚本 - 用于管理数据库中的授权码
const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'sh-cynosdbmysql-grp-qvemfela.sql.tencentcdb.com',
  port: 27144,
  user: 'root',
  password: 'Hu13541863172',
  database: 'mptime',
  charset: 'utf8mb4'
};

// 显示帮助信息
function showHelp() {
  console.log(`
代码管理脚本使用说明:

用法: node scripts/manage-codes.js <命令> [参数]

命令:
  list                    - 列出所有代码状态
  add <code> [days]       - 添加新代码 (默认30天)
  remove <code>           - 删除代码
  extend <code> <days>    - 延长代码有效期
  reset <code>            - 重置代码创建时间为当前时间
  cleanup [days]          - 清理过期超过指定天数的代码 (默认30天)
  stats                   - 显示统计信息

示例:
  node scripts/manage-codes.js list
  node scripts/manage-codes.js add NEW123 60
  node scripts/manage-codes.js extend OLD123 90
  node scripts/manage-codes.js cleanup 30
`);
}

// 列出所有代码
async function listCodes() {
  const connection = await mysql.createConnection(dbConfig);
  
  const [rows] = await connection.execute(`
    SELECT 
      code,
      remaining_uses as total_days,
      DATEDIFF(NOW(), created_at) as days_passed,
      (remaining_uses - DATEDIFF(NOW(), created_at)) as remaining_days,
      created_at,
      CASE 
        WHEN (remaining_uses - DATEDIFF(NOW(), created_at)) > 0 THEN '✅ 有效'
        ELSE '❌ 已过期'
      END as status
    FROM activation_codes 
    WHERE prefix = 'mobile'
    ORDER BY remaining_days DESC, created_at DESC
  `);
  
  console.log(`\n找到 ${rows.length} 个代码:\n`);
  
  rows.forEach((row, index) => {
    const createdDate = new Date(row.created_at).toLocaleDateString('zh-CN');
    const num = (index + 1).toString().padStart(3, ' ');
    const days = row.remaining_days.toString().padStart(3, ' ');
    console.log(`${num}. ${row.code} | 剩余 ${days} 天 | 创建于 ${createdDate} | ${row.status}`);
  });
  
  await connection.end();
}

// 添加新代码
async function addCode(code, days = 30) {
  const connection = await mysql.createConnection(dbConfig);
  
  try {
    await connection.execute(
      'INSERT INTO activation_codes (code, remaining_uses, prefix, created_at) VALUES (?, ?, ?, NOW())',
      [code, days, 'mobile']
    );
    console.log(`✅ 成功添加代码 ${code}，有效期 ${days} 天`);
  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      console.log(`❌ 代码 ${code} 已存在`);
    } else {
      console.error('❌ 添加失败:', error.message);
    }
  }
  
  await connection.end();
}

// 删除代码
async function removeCode(code) {
  const connection = await mysql.createConnection(dbConfig);
  
  const [result] = await connection.execute(
    'DELETE FROM activation_codes WHERE code = ? AND prefix = ?',
    [code, 'mobile']
  );
  
  if (result.affectedRows > 0) {
    console.log(`✅ 成功删除代码 ${code}`);
  } else {
    console.log(`❌ 代码 ${code} 不存在`);
  }
  
  await connection.end();
}

// 延长代码有效期
async function extendCode(code, days) {
  const connection = await mysql.createConnection(dbConfig);
  
  const [result] = await connection.execute(
    'UPDATE activation_codes SET remaining_uses = ? WHERE code = ? AND prefix = ?',
    [days, code, 'mobile']
  );
  
  if (result.affectedRows > 0) {
    console.log(`✅ 成功将代码 ${code} 的有效期设置为 ${days} 天`);
  } else {
    console.log(`❌ 代码 ${code} 不存在`);
  }
  
  await connection.end();
}

// 重置代码创建时间
async function resetCode(code) {
  const connection = await mysql.createConnection(dbConfig);
  
  const [result] = await connection.execute(
    'UPDATE activation_codes SET created_at = NOW() WHERE code = ? AND prefix = ?',
    [code, 'mobile']
  );
  
  if (result.affectedRows > 0) {
    console.log(`✅ 成功重置代码 ${code} 的创建时间`);
  } else {
    console.log(`❌ 代码 ${code} 不存在`);
  }
  
  await connection.end();
}

// 清理过期代码
async function cleanupCodes(expiredDays = 30) {
  const connection = await mysql.createConnection(dbConfig);
  
  const [result] = await connection.execute(
    'DELETE FROM activation_codes WHERE prefix = ? AND (remaining_uses - DATEDIFF(NOW(), created_at)) < ?',
    ['mobile', -expiredDays]
  );
  
  console.log(`✅ 清理了 ${result.affectedRows} 个过期超过 ${expiredDays} 天的代码`);
  
  await connection.end();
}

// 显示统计信息
async function showStats() {
  const connection = await mysql.createConnection(dbConfig);
  
  const [stats] = await connection.execute(`
    SELECT 
      COUNT(*) as total_codes,
      COUNT(CASE WHEN (remaining_uses - DATEDIFF(NOW(), created_at)) > 0 THEN 1 END) as valid_codes,
      COUNT(CASE WHEN (remaining_uses - DATEDIFF(NOW(), created_at)) <= 0 THEN 1 END) as expired_codes,
      AVG(remaining_uses - DATEDIFF(NOW(), created_at)) as avg_remaining_days
    FROM activation_codes 
    WHERE prefix = 'mobile'
  `);
  
  const stat = stats[0];
  console.log(`\n📊 代码统计信息:`);
  console.log(`   总代码数: ${stat.total_codes}`);
  console.log(`   有效代码: ${stat.valid_codes}`);
  console.log(`   过期代码: ${stat.expired_codes}`);
  console.log(`   平均剩余天数: ${Math.round(stat.avg_remaining_days * 100) / 100} 天`);
  
  await connection.end();
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    switch (command) {
      case 'list':
        await listCodes();
        break;
      case 'add':
        if (!args[1]) {
          console.log('❌ 请提供代码名称');
          return;
        }
        await addCode(args[1], parseInt(args[2]) || 30);
        break;
      case 'remove':
        if (!args[1]) {
          console.log('❌ 请提供代码名称');
          return;
        }
        await removeCode(args[1]);
        break;
      case 'extend':
        if (!args[1] || !args[2]) {
          console.log('❌ 请提供代码名称和天数');
          return;
        }
        await extendCode(args[1], parseInt(args[2]));
        break;
      case 'reset':
        if (!args[1]) {
          console.log('❌ 请提供代码名称');
          return;
        }
        await resetCode(args[1]);
        break;
      case 'cleanup':
        await cleanupCodes(parseInt(args[1]) || 30);
        break;
      case 'stats':
        await showStats();
        break;
      default:
        showHelp();
    }
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
  }
}

main();
