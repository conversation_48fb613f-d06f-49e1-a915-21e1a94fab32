import { ReactElement } from "react";
import satori from "satori";
import { convertClassNameToTw } from "./tw";

export async function tSvg(
  ele: ReactElement,
  options: { width: number; height: number; fontData: ArrayBuffer }
) {
  const svg = await satori(convertClassNameToTw(ele), {
    width: options.width,
    height: options.height,
    fonts: [
      {
        name: "pingfangsc",
        data: options.fontData as any,
        style: "normal",
      },
    ],
  });
  return svg;
}

export async function tPNG(
  ele: ReactElement,
  options: { width: number; height: number; fontData: ArrayBuffer }
) {
  const svg = await satori(convertClassNameToTw(ele), {
    width: options.width,
    height: options.height,
    fonts: [
      {
        name: "pingfangsc",
        data: options.fontData as any,
        style: "normal",
      },
    ],
  });

  return svg;
}
