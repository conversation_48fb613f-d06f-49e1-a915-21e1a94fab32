// @ts-nocheck
const config = {
  fontWeights: {
    "100": "Thin",
    "200": "Extra Light",
    "300": "Light",
    "400": "Regular",
    "500": "Medium",
    "600": "Semi Bold",
    "700": "Bold",
    "800": "Extra Bold",
    "900": "Black",
  },
  fonts: [
    {
      value: "inter",
      label: "Inter",
      weights: [100, 200, 300, 400, 500, 600, 700, 800, 900],
      subset: "latin",
    },
    {
      value: "open-sans",
      label: "Open Sans",
      weights: [300, 400, 500, 600, 700, 800],
      subset: "latin",
    },
    {
      value: "noto-sans",
      label: "Noto Sans",
      weights: [100, 200, 300, 400, 500, 600, 700, 800, 900],
      subset: "latin",
    },
    {
      value: "noto-sans-jp",
      label: "<PERSON>o Sans (Japanese)",
      weights: [100, 200, 300, 400, 500, 600, 700, 800, 900],
      subset: "japanese",
    },
    {
      value: "noto-sans-tc",
      label: "<PERSON><PERSON> Sans (Traditional Chinese)",
      weights: [100, 200, 300, 400, 500, 600, 700, 800, 900],
      subset: "chinese-traditional",
    },
    {
      value: "noto-sans-sc",
      label: "Noto Sans (Simplified Chinese)",
      weights: [100, 200, 300, 400, 500, 600, 700, 800, 900],
      subset: "chinese-simplified",
    },
    {
      value: "roboto",
      label: "Roboto",
      weights: [100, 300, 400, 500, 700, 900],
      subset: "latin",
    },
    {
      value: "poppins",
      label: "Poppins",
      weights: [100, 200, 300, 400, 500, 600, 700, 800, 900],
      subset: "latin",
    },
    {
      value: "montserrat",
      label: "Montserrat",
      weights: [100, 200, 300, 400, 500, 600, 700, 800, 900],
      subset: "latin",
    },
    {
      value: "lato",
      label: "Lato",
      weights: [100, 300, 400, 700, 900],
      subset: "latin",
    },
    {
      value: "manrope",
      label: "Manrope",
      weights: [200, 300, 400, 500, 600, 700, 800],
      subset: "latin",
    },
    {
      value: "ubuntu",
      label: "Ubuntu",
      weights: [300, 400, 500, 700],
      subset: "latin",
    },
    {
      value: "figtree",
      label: "Figtree",
      weights: [300, 400, 500, 600, 700, 800, 900],
      subset: "latin",
    },
    {
      value: "fira-sans",
      label: "Fira Sans",
      weights: [100, 200, 300, 400, 500, 600, 700, 800, 900],
      subset: "latin",
    },
    {
      value: "fira-code",
      label: "Fira Code",
      weights: [300, 400, 500, 600, 700],
      subset: "latin",
    },
    {
      value: "fira-mono",
      label: "Fira Mono",
      weights: [400, 500, 700],
      subset: "latin",
    },
    {
      value: "source-code-pro",
      label: "Source Code Pro",
      weights: [200, 300, 400, 500, 600, 700, 800, 900],
      subset: "latin",
    },
    {
      value: "ibm-plex-mono",
      label: "IBM Plex Mono",
      weights: [100, 200, 300, 400, 500, 600, 700],
      subset: "latin",
    },
    {
      value: "jetbrains-mono",
      label: "JetBrains Mono",
      weights: [100, 200, 300, 400, 500, 600, 700, 800],
      subset: "latin",
    },
  ],
  defaultWeight: 400,
  defaultFont: "inter",
  cdnBaseUrl: "https://cdn.jsdelivr.net/fontsource/fonts/",
};

export const font = async (font: string, weight: string) => {
  const fontConfig = config.fonts.find((f) => f.value === font);
  console.log(fontConfig, font);

  // https://cdn.jsdelivr.net/fontsource/fonts/inter@latest/latin-400-normal.woff 
  const fontUrl = `${config.cdnBaseUrl}${fontConfig!.value}@latest/${fontConfig!.subset}-${weight}-normal.woff`;
  const data = await fetch(fontUrl);
  return data.arrayBuffer();
};

export const fontPingFangScMedium = async (name: string) => {
  const fontUrl = 'https://cdn.jsdelivr.net/gh/xiaoxiunique/assets@main/PingFangSC-Medium.woff'
  let buffer;
  if (window.__resource && window.__resource[fontUrl]) {
    buffer = window.__resource[fontUrl];
  } else {
    const data = await fetch(fontUrl);
    buffer = await data.arrayBuffer();
    window.__resource = window.__resource || {};
    window.__resource[fontUrl] = buffer;
  }

  return [
    {
      name: name,
      weight: "500",
      style: "normal",
      data: buffer,
    },
  ];
};

export const fontInner = async (name: string) => {
  console.log('load font', name);
  return fontPingFangScMedium(name);

  const inner = config.fonts.find((f) => f.value === "inter");
  if (!inner) return [];

  const fontPromises = inner.weights.map(async (weight) => {
    const data = await font("inter", weight.toString());
    return {
      name: "inner-" + weight.toString(),
      weight: weight.toString() as unknown as
        | 100
        | 200
        | 300
        | 400
        | 500
        | 600
        | 700
        | 800
        | 900,
      style: "normal",
      data,
    };
  });

  return Promise.all(fontPromises);
};

export const supportFonts = [
  {
    name: "pingfangsc",
    value: "pingfangsc-medium.ttf",
  },
  {
    name: "ming",
    value: "ming.otf",
  },
  {
    name: "new",
    value: "new.otf",
  },
];