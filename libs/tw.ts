// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import React from "react";
import { ReactElement } from "react";

export const convertClassNameToTw = (element: ReactElement): ReactElement => {
  if (
    typeof element === "string" ||
    typeof element === "number" ||
    typeof element === "boolean"
  ) {
    return element;
  }

  const { props, type } = element;

  if (typeof type === "function") {
    // 如果是自定义组件，我们需要先渲染它
    return convertClassNameToTw(type(props));
  }

  const newProps = { ...props };
  if (props.className) {
    // 检查是否已经包含 flex，如果没有则添加
    const classes = props.className.split(" ");
    if (!classes.includes("flex")) {
      classes.unshift("flex");
    }
    newProps.tw = classes.join(" ");
    delete newProps.className;
  } else {
    // 如果没有 className，直接添加 flex
    newProps.tw = "flex";
  }

  if (props.children) {
    newProps.children = React.Children.map(props.children, (child) =>
      React.isValidElement(child) ? convertClassNameToTw(child) : child
    );
  }

  return React.createElement(type, newProps);
};
