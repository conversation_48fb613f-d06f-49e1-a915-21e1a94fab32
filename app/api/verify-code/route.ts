import { NextResponse } from 'next/server';
import { getCodeInfo } from '@/lib/code';
import { checkAccessLimit } from '@/lib/rateLimit';

export async function POST(request: Request) {
  const { code } = await request.json();

  // 每日限流逻辑
  const { allowed, count } = await checkAccessLimit(code);
  if (!allowed) {
    return NextResponse.json({ success: false, message: '今日访问次数已达上限', count }, { status: 429 });
  }

  // 从数据库验证代码并获取详细信息
  try {
    const codeInfo = await getCodeInfo(code);
    if (codeInfo.isValid) {
      return NextResponse.json({
        success: true,
        count: 1,
        remainingDays: codeInfo.remainingDays,
        totalDays: codeInfo.totalDays,
        daysPassed: codeInfo.daysPassed
      });
    } else {
      let message = '无效的授权码';
      if (codeInfo.remainingDays <= 0 && codeInfo.totalDays > 0) {
        message = `授权码已过期（已过期 ${Math.abs(codeInfo.remainingDays)} 天）`;
      }
      return NextResponse.json({
        success: false,
        message,
        count: 1,
        remainingDays: codeInfo.remainingDays,
        totalDays: codeInfo.totalDays,
        daysPassed: codeInfo.daysPassed
      }, { status: 401 });
    }
  } catch (error) {
    console.error('验证代码时发生错误:', error);
    return NextResponse.json({ success: false, message: '服务器错误，请稍后重试', count: 1 }, { status: 500 });
  }
}