import { validateCodeFromDB } from "@/lib/code";
import BillPreview from "./BillPreview";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "话费账单",
};

export default async function PreviewPage({
  params,
}: {
  params: Promise<{ accessCode: string }>;
}) {
  const { accessCode } = await params;

  let isValidCode = false;
  try {
    isValidCode = await validateCodeFromDB(accessCode);
  } catch (error) {
    console.error('验证代码时发生错误:', error);
  }

  if (!isValidCode) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-red-500 text-2xl">无效的授权码</div>
      </div>
    );
  }

  return <BillPreview />;
}