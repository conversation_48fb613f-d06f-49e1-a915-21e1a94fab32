"use client";
import { useState, useEffect, useRef } from "react";
import MobileBill from "@/components/MobileBill";
import { PackageItem, DiscountItem } from "@/components/MobileBill";

const initialPackages: PackageItem[] = [
  { name: "惠民爱家58", originalPrice: 58 },
  { name: "悦享6G套餐", originalPrice: 68, infoIcon: true },
];

const initialDiscountItems: DiscountItem[] = [
  { name: "惠民爱家58资费优惠", discount: -58 },
];

export default function BillPreview() {
  const [name, setName] = useState("胡*");
  const [mobile, setMobile] = useState("135****3173");
  const [broadbandNumber, setBroadbandNumber] = useState("209****1766");
  const [broadbandAmount, setBroadbandAmount] = useState(0);
  const [packages, setPackages] = useState<PackageItem[]>(initialPackages);
  const [discountItems, setDiscountItems] =
    useState<DiscountItem[]>(initialDiscountItems);
  const billRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    try {
      const storedConfig = localStorage.getItem("billConfig");
      if (storedConfig) {
        const config = JSON.parse(storedConfig);
        setName(config.name || "胡*");
        setMobile(config.mobile || "135****3173");
        setBroadbandNumber(config.broadbandNumber || "209****1766");
        setBroadbandAmount(config.broadbandAmount || 0);
        setPackages(
          config.packages && config.packages.length > 0
            ? config.packages
            : initialPackages
        );
        setDiscountItems(
          config.discountItems && config.discountItems.length > 0
            ? config.discountItems
            : initialDiscountItems
        );
      }
    } catch (error) {
      console.error("Failed to parse config from localStorage", error);
    }
  }, []);

  return (
    <div className="w-full max-w-md mx-auto" ref={billRef}>
      <MobileBill
        name={name}
        mobile={mobile}
        packages={packages}
        broadbandNumber={broadbandNumber}
        broadbandAmount={broadbandAmount}
        discountItems={discountItems}
      />
    </div>
  );
} 