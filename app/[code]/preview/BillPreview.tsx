"use client";
import { useState, useEffect, useRef } from "react";
import MobileBill from "@/components/MobileBill";
import { PackageItem, DiscountItem } from "@/components/MobileBill";

const initialPackages: PackageItem[] = [
  { name: "惠民爱家58", originalPrice: 58 },
  { name: "悦享6G套餐", originalPrice: 68, infoIcon: true },
];

const initialDiscountItems: DiscountItem[] = [
  { name: "惠民爱家58资费优惠", discount: -58 },
];

export default function BillPreview() {
  const [name, setName] = useState("胡*");
  const [mobile, setMobile] = useState("135****3173");
  const [broadbandNumber, setBroadbandNumber] = useState("209****1766");
  const [broadbandAmount, setBroadbandAmount] = useState(0);
  const [packages, setPackages] = useState<PackageItem[]>(initialPackages);
  const [discountItems, setDiscountItems] =
    useState<DiscountItem[]>(initialDiscountItems);
  const billRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    try {
      const storedConfig = localStorage.getItem("billConfig");
      if (storedConfig) {
        const config = JSON.parse(storedConfig);
        setName(config.name || "胡*");
        setMobile(config.mobile || "135****3173");
        setBroadbandNumber(config.broadbandNumber || "209****1766");
        setBroadbandAmount(config.broadbandAmount || 0);
        setPackages(
          config.packages && config.packages.length > 0
            ? config.packages
            : initialPackages
        );
        setDiscountItems(
          config.discountItems && config.discountItems.length > 0
            ? config.discountItems
            : initialDiscountItems
        );
      }

      // 这个 fetch 可能会执行两次的原因通常是 React 严格模式（StrictMode）在开发环境下会让 useEffect 里的副作用执行两次，以帮助开发者发现副作用相关的问题。
      // 生产环境下只会执行一次。可以在控制台查看是否有 React.StrictMode 包裹了应用。
      // fetch('https://api.day.app/nnwQnBZ4wcp5Gd8zveakZB/话费账单').then(res => {
      //   console.log(res);
      // });
    } catch (error) {
      console.error("Failed to parse config from localStorage", error);
    }
  }, []);

  return (
    <div className="w-full max-w-md mx-auto" ref={billRef}>
      <MobileBill
        name={name}
        mobile={mobile}
        packages={packages}
        broadbandNumber={broadbandNumber}
        broadbandAmount={broadbandAmount}
        discountItems={discountItems}
      />
    </div>
  );
} 