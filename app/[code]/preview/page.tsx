import { validateCodeFromDB } from "@/lib/code";
import BillPreview from "./BillPreview";

// Set page title
const pageTitle = "话费账单";
if (typeof document !== "undefined") {
  document.title = pageTitle;
}

export default async function PreviewPage({ params }: { params: Promise<{ code: string }> }) {
  const { code } = await params;

  let isValidCode = false;
  try {
    isValidCode = await validateCodeFromDB(code);
  } catch (error) {
    console.error('验证代码时发生错误:', error);
  }

  if (!isValidCode) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-red-500 text-2xl">无效的授权码</div>
      </div>
    );
  }

  return <BillPreview />;
}