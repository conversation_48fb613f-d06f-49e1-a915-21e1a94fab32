"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface Package {
  name: string;
  originalPrice: number | string;
  infoIcon: boolean;
}
interface Discount {
  name: string;
  discount: number | string;
}

export default function Home() {
  const [name, setName] = useState("胡*");
  const [mobile, setMobile] = useState(() => {
    const prefixes = [
      "134",
      "135",
      "136",
      "137",
      "138",
      "139",
      "147",
      "150",
      "151",
      "152",
      "157",
      "158",
      "159",
      "178",
      "182",
      "183",
      "184",
      "187",
      "188",
      "198",
    ];
    const randomPrefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const randomSuffix = Math.floor(Math.random() * 100000000)
      .toString()
      .padStart(8, "0");
    return randomPrefix + randomSuffix;
  });
  const [mobileError, setMobileError] = useState("");
  const [broadbandNumber, setBroadbandNumber] = useState("20917661766");
  const [broadbandAmount, setBroadbandAmount] = useState<number | string>(0);
  const [packages, setPackages] = useState<Package[]>([
    { name: "惠民爱家58", originalPrice: 58, infoIcon: false },
    { name: "悦享6G套餐", originalPrice: 68, infoIcon: true },
  ]);
  const [discountItems, setDiscountItems] = useState<Discount[]>([
    { name: "惠民爱家58资费优惠", discount: -58 },
  ]);
  const [code, setCode] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [authCode, setAuthCode] = useState("");
  const [authCodeError, setAuthCodeError] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const router = useRouter();

  useEffect(() => {
    try {
      const storedConfig = localStorage.getItem("billConfig");
      if (storedConfig) {
        const config = JSON.parse(storedConfig);
        if (config.code) {
          router.push(`/${config.code}/preview`);
          return;
        }
        setName(config.name || "胡*");
        setMobile(config.mobile || "13531733173");
        setBroadbandNumber(config.broadbandNumber || "20917661766");
        setBroadbandAmount(config.broadbandAmount || 0);
        setPackages(config.packages || []);
        setDiscountItems(config.discountItems || []);
        setCode(config.code || "");
        setAuthCode(config.code || "");
      }
    } catch (error) {
      console.error("Failed to parse config from localStorage", error);
    }
  }, []);

  useEffect(() => {
    const config = {
      name,
      mobile,
      broadbandNumber,
      broadbandAmount: parseFloat(broadbandAmount as string) || 0,
      packages: packages.map((p) => ({
        ...p,
        originalPrice: parseFloat(p.originalPrice as string) || 0,
      })),
      discountItems: discountItems.map((d) => ({
        ...d,
        discount: parseFloat(d.discount as string) || 0,
      })),
      code,
    };
    localStorage.setItem("billConfig", JSON.stringify(config));
  }, [name, mobile, broadbandNumber, broadbandAmount, packages, discountItems, code]);

  const handleMobileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMobile(value);
    if (value.length !== 11 || !/^\d+$/.test(value)) {
      setMobileError("手机号必须是11位数字");
    } else {
      setMobileError("");
    }
  };

  const handlePreviewClick = () => {
    setIsDialogOpen(true);
  };

  const handleConfirm = async () => {
    if (!authCode) {
      setAuthCodeError("请输入授权码");
      return;
    }
    setAuthCodeError("");
    setIsVerifying(true);
    try {
      const response = await fetch("/api/verify-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ code: authCode }),
      });

      if (response.ok) {
        setCode(authCode);
        setIsDialogOpen(false);
        router.push(`/${authCode}/preview`);
      } else {
        const errorData = await response.json();
        setAuthCodeError(errorData.message || "授权码无效");
      }
    } catch (error) {
      console.error("Verification failed:", error);
      setAuthCodeError("验证失败，请稍后重试");
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <div className="flex flex-col items-center min-h-screen bg-gray-50">
      <Card className="w-full max-w-3xl">
        <CardHeader className="sticky top-0 bg-white z-10 shadow-sm">
          <div className="flex justify-between items-center">
            <CardTitle className="text-2xl font-bold">配置账单参数</CardTitle>
            <Button
              variant="link"
              onClick={handlePreviewClick}
              className="text-sm p-0 h-auto min-w-0 underline underline-offset-2 text-blue-600 hover:text-blue-800"
            >
              前往预览
            </Button>
          </div>
          {/* notes */}
          {/* <div className="flex justify-between items-center">
            <p className="text-sm text-red-500">使用者请+客服QQ: 3661407208</p>
          </div> */}
        </CardHeader>
        <CardContent>
          <form
            className="flex flex-col gap-6"
            onSubmit={(e) => e.preventDefault()}
          >
            <div className="grid sm:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">姓名</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="mobile">手机号</Label>
                <Input
                  id="mobile"
                  value={mobile}
                  onChange={handleMobileChange}
                  maxLength={11}
                />
                {mobileError && (
                  <p className="text-red-500 text-xs mt-1">{mobileError}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="broadbandNumber">宽带号</Label>
                <Input
                  id="broadbandNumber"
                  value={broadbandNumber}
                  onChange={(e) => setBroadbandNumber(e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="broadbandAmount">宽带消费金额</Label>
              <Input
                id="broadbandAmount"
                value={broadbandAmount}
                onChange={(e) => setBroadbandAmount(e.target.value)}
              />
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-lg text-gray-800">套餐</h3>
              {packages.map((pkg, index) => (
                <div
                  key={index}
                  className="flex flex-col sm:flex-row gap-4 items-center p-4 bg-gray-50 rounded-md border"
                >
                  <div className="flex-1 w-full">
                    <Label htmlFor={`pkg-name-${index}`} className="sr-only">
                      套餐名称
                    </Label>
                    <Input
                      id={`pkg-name-${index}`}
                      placeholder="套餐名称"
                      value={pkg.name}
                      onChange={(e) => {
                        const newPackages = [...packages];
                        newPackages[index] = { ...pkg, name: e.target.value };
                        setPackages(newPackages);
                      }}
                    />
                  </div>
                  <div className="flex gap-4 items-center w-full sm:w-auto">
                    <div className="w-full sm:w-32">
                      <Label htmlFor={`pkg-price-${index}`} className="sr-only">
                        原价
                      </Label>
                      <Input
                        id={`pkg-price-${index}`}
                        placeholder="原价"
                        value={pkg.originalPrice}
                        onChange={(e) => {
                          const newPackages = [...packages];
                          newPackages[index] = {
                            ...pkg,
                            originalPrice: e.target.value,
                          };
                          setPackages(newPackages);
                        }}
                      />
                    </div>
                    <Button
                      variant="destructive"
                      size="icon"
                      onClick={() =>
                        setPackages(packages.filter((_, i) => i !== index))
                      }
                    >
                      <span className="sr-only">删除</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M3 6h18" />
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                        <line x1="10" x2="10" y1="11" y2="17" />
                        <line x1="14" x2="14" y1="11" y2="17" />
                      </svg>
                    </Button>
                  </div>
                </div>
              ))}
              <Button
                variant="outline"
                onClick={() =>
                  setPackages([
                    ...packages,
                    { name: "", originalPrice: 0, infoIcon: false },
                  ])
                }
              >
                添加套餐
              </Button>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-lg text-gray-800">优惠</h3>
              {discountItems.map((item, index) => (
                <div
                  key={index}
                  className="flex flex-col sm:flex-row gap-4 items-center p-4 bg-gray-50 rounded-md border"
                >
                  <div className="flex-1 w-full">
                    <Label
                      htmlFor={`discount-name-${index}`}
                      className="sr-only"
                    >
                      优惠名称
                    </Label>
                    <Input
                      id={`discount-name-${index}`}
                      placeholder="优惠名称"
                      value={item.name}
                      onChange={(e) => {
                        const newItems = [...discountItems];
                        newItems[index] = { ...item, name: e.target.value };
                        setDiscountItems(newItems);
                      }}
                    />
                  </div>
                  <div className="flex gap-4 items-center w-full sm:w-auto">
                    <div className="w-full sm:w-32">
                      <Label
                        htmlFor={`discount-amount-${index}`}
                        className="sr-only"
                      >
                        优惠金额
                      </Label>
                      <Input
                        id={`discount-amount-${index}`}
                        placeholder="优惠金额"
                        value={item.discount}
                        onChange={(e) => {
                          const newItems = [...discountItems];
                          newItems[index] = {
                            ...item,
                            discount: e.target.value,
                          };
                          setDiscountItems(newItems);
                        }}
                      />
                    </div>
                    <Button
                      variant="destructive"
                      size="icon"
                      onClick={() =>
                        setDiscountItems(
                          discountItems.filter((_, i) => i !== index)
                        )
                      }
                    >
                      <span className="sr-only">删除</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M3 6h18" />
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                        <line x1="10" x2="10" y1="11" y2="17" />
                        <line x1="14" x2="14" y1="11" y2="17" />
                      </svg>
                    </Button>
                  </div>
                </div>
              ))}
              <Button
                variant="outline"
                onClick={() =>
                  setDiscountItems([
                    ...discountItems,
                    { name: "", discount: 0 },
                  ])
                }
              >
                添加优惠
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>请输入授权码</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 items-center gap-4">
              <Label htmlFor="name" className="text-left">
                授权码
              </Label>
              <Input
                id="name"
                value={authCode}
                onChange={(e) => setAuthCode(e.target.value)}
                className="col-span-3"
              />
            </div>
            {authCodeError && (
              <p className="text-red-500 text-xs col-start-2 col-span-3">
                {authCodeError}
              </p>
            )}
          </div>
          <DialogFooter>
            <Button onClick={handleConfirm} disabled={isVerifying}>
              {isVerifying ? "验证中..." : "确定"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
