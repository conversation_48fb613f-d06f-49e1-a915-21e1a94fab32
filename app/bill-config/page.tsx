"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function BillConfig() {
  const router = useRouter();
  const [mobile, setMobile] = useState("135****3173");
  const [broadbandNumber, setBroadbandNumber] = useState("209****1766");
  const [broadbandAmount, setBroadbandAmount] = useState(0);
  const [packages, setPackages] = useState([
    { name: "惠民爱家58", originalPrice: 58 },
    { name: "悦享6G套餐", originalPrice: 68, infoIcon: true },
  ]);
  const [discountItems, setDiscountItems] = useState([
    { name: "惠民爱家58资费优惠", discount: -58 },
  ]);

  // 简单表单，后续可用shadcn/ui优化
  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    // 跳转到账单页面并带参数（可用query或localStorage等方式）
    // 这里用localStorage做简单演示
    localStorage.setItem(
      "billConfig",
      JSON.stringify({
        mobile,
        broadbandNumber,
        broadbandAmount,
        packages,
        discountItems,
      })
    );
    router.push("/bill-preview");
  }

  return (
    <div className="max-w-xl mx-auto p-6 bg-white rounded shadow mt-8">
      <h2 className="text-xl font-bold mb-4">配置账单参数</h2>
      <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
        <label className="flex flex-col gap-1">
          手机号
          <input
            className="border rounded px-2 py-1"
            value={mobile}
            onChange={(e) => setMobile(e.target.value)}
          />
        </label>
        <label className="flex flex-col gap-1">
          宽带号
          <input
            className="border rounded px-2 py-1"
            value={broadbandNumber}
            onChange={(e) => setBroadbandNumber(e.target.value)}
          />
        </label>
        <label className="flex flex-col gap-1">
          宽带消费金额
          <input
            type="number"
            className="border rounded px-2 py-1"
            value={broadbandAmount}
            onChange={(e) => setBroadbandAmount(Number(e.target.value))}
          />
        </label>
        {/* 套餐和优惠可做动态增删，这里简单输入 */}
        <label className="flex flex-col gap-1">
          套餐（JSON数组）
          <textarea
            className="border rounded px-2 py-1"
            rows={3}
            value={JSON.stringify(packages, null, 2)}
            onChange={(e) => {
              try {
                setPackages(JSON.parse(e.target.value));
              } catch {}
            }}
          />
        </label>
        <label className="flex flex-col gap-1">
          优惠（JSON数组）
          <textarea
            className="border rounded px-2 py-1"
            rows={2}
            value={JSON.stringify(discountItems, null, 2)}
            onChange={(e) => {
              try {
                setDiscountItems(JSON.parse(e.target.value));
              } catch {}
            }}
          />
        </label>
        <button
          type="submit"
          className="bg-blue-600 text-white rounded px-4 py-2 hover:bg-blue-700"
        >
          确定
        </button>
      </form>
    </div>
  );
}
