"use client";
import { useEffect, useState } from "react";
import MobileBill, { MobileBillProps } from "@/components/MobileBill";

export default function BillPreview() {
  const [config, setConfig] = useState<MobileBillProps | null>(null);

  useEffect(() => {
    const data = localStorage.getItem("billConfig");
    if (data) setConfig(JSON.parse(data));
  }, []);

  if (!config) return <div className="p-8 text-center">加载中...</div>;

  return (
    <div className="p-4">
      <MobileBill {...config} />
    </div>
  );
} 