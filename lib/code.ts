import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'sh-cynosdbmysql-grp-qvemfela.sql.tencentcdb.com',
  port: 27144,
  user: 'root',
  password: 'Hu13541863172',
  database: 'mptime',
  charset: 'utf8mb4'
};

/**
 * 数据库表结构
 * -- auto-generated definition
create table activation_codes
(
    code           varchar(191)                        not null
        primary key,
    remaining_uses int                                 not null,
    open_id        varchar(191)                        null,
    prefix         varchar(120)                        null,
    created_at     timestamp default CURRENT_TIMESTAMP null
)
    collate = utf8mb4_unicode_ci;
 */

// 创建数据库连接池
let pool: mysql.Pool | null = null;

function getPool() {
  if (!pool) {
    pool = mysql.createPool({
      ...dbConfig,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });
  }
  return pool;
}

/**
 * 计算从创建时间到现在过了多少天
 * @param createdAt 创建时间
 * @returns 已过天数
 */
function getDaysFromCreation(createdAt: Date): number {
  const now = new Date();
  const diffTime = now.getTime() - createdAt.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}

/**
 * 从数据库查询激活码并验证是否仍有效
 * @param code 要查询的代码
 * @returns 如果代码有效且前缀为 'mobile' 且未过期则返回 true，否则返回 false
 */
export async function validateCodeFromDB(code: string): Promise<boolean> {
  try {
    const connection = getPool();
    const [rows] = await connection.execute(
      'SELECT code, prefix, remaining_uses, created_at FROM activation_codes WHERE code = ? AND prefix = ?',
      [code, 'mobile']
    );

    const results = rows as any[];
    if (results.length === 0) {
      return false;
    }

    const record = results[0];
    const totalDays = record.remaining_uses; // remaining_uses 实际是总天数
    const createdAt = new Date(record.created_at);
    const daysPassed = getDaysFromCreation(createdAt);
    const remainingDays = totalDays - daysPassed;

    console.log(`代码 ${code}: 总天数=${totalDays}, 已过天数=${daysPassed}, 剩余天数=${remainingDays}`);

    return remainingDays > 0;
  } catch (error) {
    console.error('Database query error:', error);
    return false;
  }
}

/**
 * 获取代码的详细信息
 * @param code 要查询的代码
 * @returns 代码信息对象，包含是否有效、剩余天数等信息
 */
export async function getCodeInfo(code: string): Promise<{
  isValid: boolean;
  totalDays: number;
  daysPassed: number;
  remainingDays: number;
  createdAt: Date | null;
}> {
  try {
    const connection = getPool();
    const [rows] = await connection.execute(
      'SELECT code, prefix, remaining_uses, created_at FROM activation_codes WHERE code = ? AND prefix = ?',
      [code, 'mobile']
    );

    const results = rows as any[];
    if (results.length === 0) {
      return {
        isValid: false,
        totalDays: 0,
        daysPassed: 0,
        remainingDays: 0,
        createdAt: null
      };
    }

    const record = results[0];
    const totalDays = record.remaining_uses; // remaining_uses 实际是总天数
    const createdAt = new Date(record.created_at);
    const daysPassed = getDaysFromCreation(createdAt);
    const remainingDays = totalDays - daysPassed;

    return {
      isValid: remainingDays > 0,
      totalDays,
      daysPassed,
      remainingDays,
      createdAt
    };
  } catch (error) {
    console.error('Database query error:', error);
    return {
      isValid: false,
      totalDays: 0,
      daysPassed: 0,
      remainingDays: 0,
      createdAt: null
    };
  }
}

/**
 * 检查代码是否在数据库中存在（用于向后兼容）
 * @param code 要检查的代码
 * @returns 如果代码存在则返回 true
 */
export async function isValidCode(code: string): Promise<boolean> {
  return await validateCodeFromDB(code);
}

// 保留原有的静态代码列表作为备用（可选）
export const codeList = [
  "kL9rTq1b", "oI3uYt7e", "aG5hJk2l", "bN0mPq4r",
  "sD6fGj8k", "fH2jKl4n", "gY5tRe6w", "jK1lMn3b",
  "qW2eRt4y", "wE7rT9yU",
  "fG1hIj3k", "hI5jKl7m", "lM3nOp5q",
  "wX9yYz1z", "yZ3aBb5c", "aB7cCd9d", "cC1dDe3f", "dE5fFg7h", "fG9hHi1j", "hI3jJk5l",
  "jK7lKm9n", "lL1mMn3o", "nN5oOp7q", "pP9qQr1s", "rR3sSt5u", "tT7uUv9w", "vV1wWx3y", "xX5yYz7z",
  "zZ9aBb1c", "bC3dDe5f", "dE7fFg9h", "fG1hHi3j", "hI5jJk7l", "jK9lKm1n", "lL3mMn5o", "nN7oOp9q",
  "pP1qQr3s", "rR5sSt7u", "tT9uUv1w", "vV3wWx5y", "xX7yYz9z", "zZ1aBb3c", "bC5dDe7f", "dE9fFg1h",
  "fG3hHi5j", "hI7jJk9l", "jK1lKm3n", "lL5mMn7o", "nN9oOp1q", "pP3qQr5s", "rR7sSt9u", "tT1uUv3w",
  "vV5wWx7y", "xX9yYz1z", "zZ3aBb5c", "bC7dDe9f", "dE1fFg3h", "fG5hHi7j", "hI9jJk1l", "jK3lKm5n",
  "lL7mMn9o", "nN1oOp3q", "pP5qQr7s", "rR9sSt1u", "tT3uUv5w", "vV7wWx9y", "xX1yYz3z", "zZ5aBb7c",
  "aB9cCd1d", "cE3dDf5e", "eF7gGh9h", "gH1iIj3j", "iJ5kKl7k", "kL9mMn1m", "mN3oOp5p", "oP7qQr9q",
  "qR1sSt3t", "sT5uUv7u", "uV9wWx1x", "wX3yYz5y", "yZ7zAb9b", "aC1bBd3c", "cD5eEf7f", "eF9gGh1h",
  "gH3iIj5j", "iJ7kKl9l", "kL1mMn3n", "mN5oOp7p", "oP9qQr1r", "qR3sSt5t", "sT7uUv9v", "uV1wWx3x",
  "wX5yYz7z", "wX9yYz1z", "yZ3aBb5c", "bC7dDe9f", "dE1fFg3h", "fG5hHi7j", "hI9jJk1l", "jK3lKm5n",
  "lL7mMn9o", "nN1oOp3q", "pP5qQr7s", "rR9sSt1u", "tT3uUv5w", "vV7wWx9y", "xX1yYz3z", "zZ5aBb7c",
  "aB9cCd1d", "cC1dDe3f", "dE5fFg7h", "fG9hHi1j", "hI3jJk5l", "jK7lKm9n", "lL1mMn3o", "nN5oOp7q",
  "pP9qQr1s", "rR3sSt5u", "tT7uUv9w", "vV1wWx3y", "xX5yYz7z", "zZ9aBb1c", "bC3dDe5f", "dE7fFg9h",
  "fG1hHi3j", "hI5jJk7l", "jK9lKm1n", "lL3mMn5o"
];