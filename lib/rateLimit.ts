import client from "./redis";

export async function checkAccessLimit(code: string): Promise<{ allowed: boolean; count: number }> {
  const today = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
  const key = `access_code:${code}:${today}`;

  const isAllowed = await client.get(key);
  if (isAllowed && Number(isAllowed) > 10) {
    return {
      allowed: false,
      count: Number(isAllowed),
    };
  }

  const count = await client.incr(key);
  if (count === 1) {
    // 第一次访问，设置过期时间到当天结束
    const now = new Date();
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0);
    const seconds = Math.floor((endOfDay.getTime() - now.getTime()) / 1000);
    await client.expire(key, seconds);
  }

  return {
    allowed: count <= 10,
    count,
  };
} 