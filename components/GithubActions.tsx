// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
export default function GithubActions({ projects }: { projects: { name: string; description: string; executions: { status: string; created_at: string; html_url: string; }[] }[] }) {
  return (
    <div className="w-full h-full bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-6">
      <div className="backdrop-blur-sm bg-white/90 p-6 border border-white/20 flex flex-col">
        <h1 className="text-3xl font-bold text-gray-800 mb-3 flex flex-col">
          Action Time
          <span className="block text-base font-normal text-gray-600 mt-1">
            GitHub Actions 执行状态监控
          </span>
        </h1>
        {projects.map((project) => (
          <div
            className="group transition-all rounded-lg py-2 flex flex-col"
            key={project.name}
          >
            <div className="flex justify-between items-center mb-1">
              <h2 className="text-xl font-medium text-gray-800">
                {project.name}
              </h2>
              <span className="text-sm text-gray-500">
                最近{project.executions.length}次执行
              </span>
            </div>
            <div className="flex items-center flex-wrap">
              {project.executions.map((run, i) => (
                <a
                  href={run.html_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  key={i}
                  className={`
                        w-3 h-3 rounded-sm transition-colors m-1
                        ${
                          run.status === "success"
                            ? "bg-green-500 hover:bg-green-600"
                            : run.status === "failed"
                            ? "bg-red-500 hover:bg-red-600"
                            : "bg-gray-200 hover:bg-gray-300"
                        }
                        cursor-pointer
                      `}
                  title={`${
                    run.status === "success"
                      ? "成功"
                      : run.status === "failed"
                      ? "失败"
                      : "未执行"
                  }
                              ${new Date(run.created_at).toLocaleString()}`}
                ></a>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
