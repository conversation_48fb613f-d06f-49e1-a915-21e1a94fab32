import React from "react";

export interface PackageItem {
  name: string;
  originalPrice: number;
  discount?: number; // 优惠金额，负数
  actualPrice?: number; // 可选，若有特殊实际消费
  infoIcon?: boolean;
}

export interface DiscountItem {
  name: string;
  discount: number; // 负数
}

export interface MobileBillProps {
  name: string;
  mobile: string;
  packages: PackageItem[];
  broadbandNumber: string;
  broadbandAmount?: number; // 宽带消费金额，默认0
  broadbandLabel?: string;
  discountItems?: DiscountItem[];
}

export default function MobileBill({
  name = "胡*",
  mobile = "135****3173",
  packages = [
    { name: "惠民爱家58", originalPrice: 58 },
    { name: "悦享6G套餐", originalPrice: 68, infoIcon: true },
  ],
  broadbandNumber = "209****1766",
  broadbandAmount = 0,
  broadbandLabel = "宽带",
  discountItems = [
    { name: "惠民爱家58资费优惠", discount: -58 },
  ],
}: MobileBillProps) {
  // 获取当前时间字符串
  const now = new Date();
  const pad = (n: number) => n.toString().padStart(2, '0');
  const billTime = `${now.getFullYear()}年${pad(now.getMonth() + 1)}月${pad(now.getDate())}日 ${pad(now.getHours())}:${pad(now.getMinutes())}`;

  // 计算合计
  const totalOriginal = packages.reduce((sum, item) => sum + item.originalPrice, 0);
  const totalDiscount = (discountItems?.reduce((sum, item) => sum + item.discount, 0) ?? 0);
  // 主号套餐实际消费
  const mainAmount = packages.reduce((sum, item) => sum + (typeof item.actualPrice === 'number' ? item.actualPrice : item.originalPrice), 0) + totalDiscount;
  // 合账账户总额 = 主号+宽带
  const totalAmount = mainAmount + (broadbandAmount ?? 0);

  const mask = (str: string, prefixLength: number, suffixLength: number) => {
    if (!str || str.length <= prefixLength + suffixLength) {
      return str;
    }
    const prefix = str.substring(0, prefixLength);
    const suffix = str.substring(str.length - suffixLength);
    const middle = '*'.repeat(str.length - prefixLength - suffixLength);
    return prefix + middle + suffix;
  };
  
  const maskName = (name: string) => {
    if (!name || name.length <= 1) {
      return name;
    }
    return name.substring(0, 1) + '*'.repeat(name.length - 1);
  }

  const maskedMobile = mask(mobile, 3, 4);
  const maskedBroadband = mask(broadbandNumber, 3, 4);
  const maskedName = maskName(name);

  return (
    <div className="relative pb-8 text-sm bg-[#f8f9fa] font-sans">
      {/* Header */}
      <header className="bg-[#4A90E2] text-white pt-4 pb-12">
        <div className="text-center text-lg font-bold mb-4">话费账单</div>
        <nav className="flex justify-around items-center">
          {Array.from({length: 5}).map((_, i) => {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            return (
              <div key={i} className={i === 0 ? "text-white" : "text-white/60"}>
                <span className="text-xs">{pad(date.getMonth() + 1)}月</span>
                <br />
                {date.getFullYear()}
              </div>
            );
          })}
          {/* 其余月份可按需渲染 */}
        </nav>
      </header>

      {/* Main Content */}
      <main className="relative px-3 -mt-10 flex flex-col gap-3">
        {/* Overview Card */}
        <section className="bg-white rounded-lg p-4 flex flex-col border border-gray-200">
          <div className="flex justify-between items-start mb-4">
            <div>
              <p className="font-bold text-base">{maskedMobile} ({maskedName})</p>
              <p className="text-gray-500 text-xs">{pad(now.getMonth() + 1)}月01日 - {pad(now.getMonth() + 1)}月{pad(now.getDate())}日</p>
            </div>
            <span style={{
              backgroundColor: '#E0E7FF',
              color: '#4F46E5',
              fontSize: '0.75rem',
              fontWeight: '600',
              padding: '0.25rem 0.5rem',
              borderRadius: '9999px'
            }}>
              三星客户
            </span>
          </div>

          <div className="flex text-center border-b pb-4 mb-4">
            <div className="flex-1 border-r border-gray-200 pr-4">
              <p className="text-gray-500 text-xs">您本号码共消费(元)</p>
              <p className="text-2xl font-bold mt-1">{mainAmount.toFixed(2)}</p>
            </div>
            <div className="flex-1 pl-4">
              <p className="text-gray-500 text-xs">实际应付(元)</p>
              <p className="text-2xl font-bold mt-1">{mainAmount.toFixed(2)}</p>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <p className="text-gray-500">当前话费余额: 0元</p>
            <button className="border border-[#FF9500] text-[#FF9500] text-xs font-bold py-1 px-4 rounded-full">
              充值
            </button>
          </div>
        </section>

        {/* Details Card */}
        <section className="bg-white rounded-lg p-4 flex flex-col border border-gray-200">
          {/* Header Row */}
          <div className="flex text-gray-500 pb-2 border-b font-medium">
            <div className="flex-[5]">消费项目</div>
            <div className="flex-[2] text-right">原价</div>
            <div className="flex-1 text-right">减免</div>
            <div className="flex-[2] text-right">实际消费</div>
          </div>

          {/* 套餐及固定费 */}
          <div className="py-3 flex flex-col gap-2">
            <div className="flex items-center text-blue-500 font-semibold mb-1">
              {/* database icon */}
              <span className="w-4 h-4 mr-1 inline-block align-middle">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-4 h-4"
                >
                  <ellipse cx="12" cy="5" rx="9" ry="3" />
                  <path d="M3 5v14c0 1.7 4 3 9 3s9-1.3 9-3V5" />
                  <path d="M3 12c0 1.7 4 3 9 3s9-1.3 9-3" />
                </svg>
              </span>
              <span>套餐及固定费</span>
            </div>
            {packages.map((item, idx) => (
              <div className="flex items-center" key={item.name + idx}>
                <div className="flex-[5] flex items-center">
                  <span>{item.name}</span>
                  {item.infoIcon && (
                    <span className="ml-1 text-gray-400 w-3 h-3 inline-block align-middle">
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="w-3 h-3"
                      >
                        <circle cx="12" cy="12" r="10" />
                        <line x1="12" y1="16" x2="12" y2="12" />
                        <line x1="12" y1="8" x2="12.01" y2="8" />
                      </svg>
                    </span>
                  )}
                </div>
                <div className="flex-[2] text-right">{item.originalPrice.toFixed(2)}</div>
                <div className="flex-1 text-right">{item.discount ? item.discount.toFixed(2) : ''}</div>
                <div className="flex-[2] text-right">{(typeof item.actualPrice === 'number' ? item.actualPrice : item.originalPrice).toFixed(2)}</div>
              </div>
            ))}
          </div>

          {/* 其他费用 */}
          {discountItems && discountItems.length > 0 && (
            <div className="py-3 flex flex-col gap-2">
              <div className="flex items-center text-orange-500 font-semibold mb-1">
                {/* circle-dollar-sign icon */}
                <span className="w-4 h-4 mr-1 inline-block align-middle">
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="w-4 h-4"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <path d="M16 12A4 4 0 1 1 8 12" />
                    <path d="M12 8v8" />
                    <path d="M8 12h8" />
                  </svg>
                </span>
                <span>其他费用</span>
              </div>
              {discountItems.map((item, idx) => (
                <div className="flex items-center" key={item.name + idx}>
                  <div className="flex-[5]">{item.name}</div>
                  <div className="flex-[2]"></div>
                  <div className="flex-1 text-right text-red-500">{item.discount.toFixed(2)}</div>
                  <div className="flex-[2] text-right text-red-500">{item.discount.toFixed(2)}</div>
                </div>
              ))}
            </div>
          )}

          {/* 合计 */}
          <div className="flex items-center pt-3 border-t font-bold">
            <div className="flex-[5]">合计</div>
            <div className="flex-[2] text-right">{totalOriginal.toFixed(2)}</div>
            <div className="flex-1 text-right text-red-500">{totalDiscount.toFixed(2)}</div>
            <div className="flex-[2] text-right">{mainAmount.toFixed(2)}</div>
          </div>

          {/* 参考提示 */}
          <div className="bg-gray-100 rounded-md text-center p-3 mt-4">
            <p className="text-gray-500 text-xs">
              实时费用仅供参考，请以月末结账后月结账单为准
            </p>
            <p className="text-blue-600 font-semibold mt-1">
              本月中国移动已为您节省 ¥{Math.abs(totalDiscount).toFixed(2)}元
            </p>
          </div>
        </section>

        {/* 合账账户 */}
        <section className="bg-white rounded-lg p-4 text-gray-600 text-xs leading-relaxed flex flex-col border border-gray-200">
          <p>
            合账账户消费总额: {totalAmount.toFixed(2)}元,
            其中(主号){maskedMobile}消费{mainAmount.toFixed(2)}元,({broadbandLabel}){maskedBroadband}消费{(broadbandAmount ?? 0).toFixed(2)}元,合账账户应付总额:
            {totalAmount.toFixed(2)}元
          </p>
        </section>

        {/* 温馨提示 */}
        <section className="bg-white rounded-lg p-4 flex flex-col border border-gray-200">
          <div className="flex items-center font-bold mb-2">
            <div className="w-1 h-4 bg-blue-500 rounded-full mr-2"></div>
            <span>温馨提示</span>
          </div>
          <ol className="list-decimal list-inside text-gray-600 text-xs space-y-2">
            <li>
              以上是您本月通过话费支付方式订购和使用中国移动业务/服务的消费记录,通过第三方支付的消费请您至所用支付工具中查询。
            </li>
            <li>账单生成时间: {billTime}。</li>
            <li>除特别说明, 以上&quot;国内&quot;指国内(除港澳台地区)。</li>
          </ol>
        </section>
      </main>
    </div>
  );
}
